#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球简洁预测程序
输出马尔可夫链和民间方法两组预测号码
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

class SimpleLotteryPredictor:
    """简洁双色球预测器"""
    
    def __init__(self):
        self.data = None
        self.red_balls = []
        self.blue_balls = []
        
    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(file_path)
            print(f"✅ 加载数据成功，共 {len(self.data)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        try:
            # 提取红球和蓝球数据
            red_columns = [col for col in self.data.columns if '红球' in col]
            blue_columns = [col for col in self.data.columns if '蓝球' in col]
            
            # 处理红球数据
            for _, row in self.data.iterrows():
                red_nums = []
                for col in red_columns:
                    if pd.notna(row[col]):
                        red_nums.append(int(row[col]))
                if len(red_nums) == 6:
                    self.red_balls.append(sorted(red_nums))
            
            # 处理蓝球数据
            for _, row in self.data.iterrows():
                for col in blue_columns:
                    if pd.notna(row[col]):
                        self.blue_balls.append(int(row[col]))
                        break
            
            print(f"✅ 预处理完成: {len(self.red_balls)}组红球, {len(self.blue_balls)}个蓝球")
            return True
        except Exception as e:
            print(f"❌ 数据预处理失败: {e}")
            return False
    
    def markov_prediction(self):
        """马尔可夫链预测"""
        # 红球预测
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.red_balls) - 1):
            current_set = set(self.red_balls[i])
            next_set = set(self.red_balls[i + 1])
            
            for num in range(1, 34):
                current_state = 1 if num in current_set else 0
                next_state = 1 if num in next_set else 0
                transition_count[num][(current_state, next_state)] += 1
        
        predictions = []
        for num in range(1, 34):
            current_state = 1 if num in set(self.red_balls[0]) else 0
            total_transitions = sum(transition_count[num].values())
            if total_transitions > 0:
                prob_appear = transition_count[num][(current_state, 1)] / total_transitions
                predictions.append((num, prob_appear))
        
        predictions.sort(key=lambda x: x[1], reverse=True)
        red_prediction = sorted([num for num, prob in predictions[:6]])
        
        # 蓝球预测
        blue_transition = defaultdict(lambda: defaultdict(int))
        for i in range(len(self.blue_balls) - 1):
            current_blue = self.blue_balls[i]
            next_blue = self.blue_balls[i + 1]
            blue_transition[current_blue][next_blue] += 1
        
        last_blue = self.blue_balls[0]
        if last_blue in blue_transition and blue_transition[last_blue]:
            blue_prediction = max(blue_transition[last_blue].items(), key=lambda x: x[1])[0]
        else:
            blue_counter = Counter(self.blue_balls)
            blue_prediction = blue_counter.most_common(1)[0][0]
        
        return red_prediction, blue_prediction
    
    def folk_prediction(self):
        """民间方法预测"""
        # 热号分析（最近30期）
        recent_periods = min(30, len(self.red_balls))
        recent_numbers = []
        for i in range(recent_periods):
            recent_numbers.extend(self.red_balls[i])
        
        number_freq = Counter(recent_numbers)
        hot_numbers = [num for num, freq in number_freq.most_common(10)]
        
        # 遗漏分析
        missing_count = {}
        for num in range(1, 34):
            missing_count[num] = 0
            for red_set in self.red_balls:
                if num in red_set:
                    break
                missing_count[num] += 1
        
        long_missing = sorted(missing_count.items(), key=lambda x: x[1], reverse=True)[:8]
        
        # 综合评分
        candidates = set()
        candidates.update(hot_numbers[:8])  # 热号
        candidates.update([num for num, _ in long_missing[:6]])  # 遗漏号
        
        scores = {}
        for num in candidates:
            score = 0
            # 热号加分
            if num in hot_numbers[:5]:
                score += 3
            elif num in hot_numbers[:10]:
                score += 2
            
            # 遗漏加分
            if missing_count[num] > 10:
                score += 2
            elif missing_count[num] > 5:
                score += 1
            
            scores[num] = score
        
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        red_prediction = sorted([num for num, score in sorted_candidates[:6]])
        
        # 如果不足6个，补充
        while len(red_prediction) < 6:
            for num in range(1, 34):
                if num not in red_prediction:
                    red_prediction.append(num)
                    if len(red_prediction) == 6:
                        break
        
        # 蓝球民间预测
        recent_blues = self.blue_balls[:20]
        blue_counter = Counter(recent_blues)
        
        blue_missing = {}
        for num in range(1, 17):
            blue_missing[num] = 0
            for blue in self.blue_balls:
                if blue == num:
                    break
                blue_missing[num] += 1
        
        blue_scores = {}
        for num in range(1, 17):
            score = blue_counter.get(num, 0)  # 频率分
            if blue_missing[num] > 8:
                score += 3  # 长期遗漏加分
            elif blue_missing[num] > 4:
                score += 2
            blue_scores[num] = score
        
        blue_prediction = max(blue_scores.items(), key=lambda x: x[1])[0]
        
        return sorted(red_prediction), blue_prediction

def main():
    print("=" * 50)
    print("双色球简洁预测系统")
    print("=" * 50)
    
    predictor = SimpleLotteryPredictor()
    
    # 获取文件路径
    file_path = input("请输入Excel文件路径（回车使用默认）: ").strip()
    if not file_path:
        file_path = "双色球开奖数据.xlsx"
    
    # 加载和处理数据
    if not predictor.load_data(file_path):
        return
    
    if not predictor.preprocess_data():
        return
    
    print("\n正在预测...")
    
    # 马尔可夫链预测
    markov_red, markov_blue = predictor.markov_prediction()
    
    # 民间方法预测
    folk_red, folk_blue = predictor.folk_prediction()
    
    # 输出结果
    print("\n" + "=" * 50)
    print("🎯 预测结果")
    print("=" * 50)
    
    print(f"\n🔬 马尔可夫链预测:")
    print(f"   红球: {' '.join(f'{num:02d}' for num in markov_red)}")
    print(f"   蓝球: {markov_blue:02d}")
    
    print(f"\n🎯 民间方法预测:")
    print(f"   红球: {' '.join(f'{num:02d}' for num in folk_red)}")
    print(f"   蓝球: {folk_blue:02d}")
    
    print("\n" + "=" * 50)
    print("⚠️  预测仅供参考，理性购彩")
    print("=" * 50)

if __name__ == "__main__":
    main()
