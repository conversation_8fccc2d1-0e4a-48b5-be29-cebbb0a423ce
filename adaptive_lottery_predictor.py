#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应双色球预测程序
具有记忆功能，能够根据开奖结果调整预测策略
"""

import pandas as pd
import numpy as np
import json
import os
from collections import Counter, defaultdict
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class AdaptiveLotteryPredictor:
    """自适应双色球预测器"""
    
    def __init__(self):
        self.data = None
        self.red_balls = []
        self.blue_balls = []
        self.history_file = "prediction_history.json"
        self.prediction_history = self.load_history()
        self.method_weights = {"markov": 0.5, "folk": 0.5}  # 初始权重
        
    def load_history(self):
        """加载预测历史"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {"predictions": [], "accuracy_stats": {}}
        return {"predictions": [], "accuracy_stats": {}}
    
    def save_history(self):
        """保存预测历史"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(self.prediction_history, f, ensure_ascii=False, indent=2)
    
    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(file_path)
            print(f"✅ 加载数据成功，共 {len(self.data)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        try:
            red_columns = [col for col in self.data.columns if '红球' in col]
            blue_columns = [col for col in self.data.columns if '蓝球' in col]
            
            for _, row in self.data.iterrows():
                red_nums = []
                for col in red_columns:
                    if pd.notna(row[col]):
                        red_nums.append(int(row[col]))
                if len(red_nums) == 6:
                    self.red_balls.append(sorted(red_nums))
            
            for _, row in self.data.iterrows():
                for col in blue_columns:
                    if pd.notna(row[col]):
                        self.blue_balls.append(int(row[col]))
                        break
            
            print(f"✅ 预处理完成: {len(self.red_balls)}组红球, {len(self.blue_balls)}个蓝球")
            return True
        except Exception as e:
            print(f"❌ 数据预处理失败: {e}")
            return False
    
    def markov_prediction(self):
        """马尔可夫链预测（带权重调整）"""
        # 基础马尔可夫链算法
        transition_count = defaultdict(lambda: defaultdict(int))
        
        # 根据历史准确率调整数据权重
        weight_factor = self.method_weights["markov"]
        
        for i in range(len(self.red_balls) - 1):
            current_set = set(self.red_balls[i])
            next_set = set(self.red_balls[i + 1])
            
            # 近期数据权重更高
            period_weight = weight_factor * (1 + 0.1 * (len(self.red_balls) - i) / len(self.red_balls))
            
            for num in range(1, 34):
                current_state = 1 if num in current_set else 0
                next_state = 1 if num in next_set else 0
                transition_count[num][(current_state, next_state)] += period_weight
        
        predictions = []
        for num in range(1, 34):
            current_state = 1 if num in set(self.red_balls[0]) else 0
            total_transitions = sum(transition_count[num].values())
            if total_transitions > 0:
                prob_appear = transition_count[num][(current_state, 1)] / total_transitions
                predictions.append((num, prob_appear))
        
        predictions.sort(key=lambda x: x[1], reverse=True)
        red_prediction = sorted([num for num, prob in predictions[:6]])
        
        # 蓝球预测
        blue_transition = defaultdict(lambda: defaultdict(int))
        for i in range(len(self.blue_balls) - 1):
            current_blue = self.blue_balls[i]
            next_blue = self.blue_balls[i + 1]
            blue_transition[current_blue][next_blue] += 1
        
        last_blue = self.blue_balls[0]
        if last_blue in blue_transition and blue_transition[last_blue]:
            blue_prediction = max(blue_transition[last_blue].items(), key=lambda x: x[1])[0]
        else:
            blue_counter = Counter(self.blue_balls)
            blue_prediction = blue_counter.most_common(1)[0][0]
        
        return red_prediction, blue_prediction
    
    def folk_prediction(self):
        """民间方法预测（带自适应调整）"""
        # 根据历史表现调整参数
        weight_factor = self.method_weights["folk"]
        
        # 热号分析（期数根据权重调整）
        recent_periods = min(int(30 * weight_factor), len(self.red_balls))
        recent_numbers = []
        for i in range(recent_periods):
            recent_numbers.extend(self.red_balls[i])
        
        number_freq = Counter(recent_numbers)
        hot_numbers = [num for num, freq in number_freq.most_common(10)]
        
        # 遗漏分析
        missing_count = {}
        for num in range(1, 34):
            missing_count[num] = 0
            for red_set in self.red_balls:
                if num in red_set:
                    break
                missing_count[num] += 1
        
        long_missing = sorted(missing_count.items(), key=lambda x: x[1], reverse=True)[:8]
        
        # 自适应评分（根据历史准确率调整权重）
        candidates = set()
        candidates.update(hot_numbers[:8])
        candidates.update([num for num, _ in long_missing[:6]])
        
        scores = {}
        for num in candidates:
            score = 0
            # 热号权重根据历史表现调整
            if num in hot_numbers[:5]:
                score += 3 * weight_factor
            elif num in hot_numbers[:10]:
                score += 2 * weight_factor
            
            # 遗漏权重调整
            if missing_count[num] > 10:
                score += 2 * (2 - weight_factor)  # 权重低时更重视遗漏
            elif missing_count[num] > 5:
                score += 1 * (2 - weight_factor)
            
            scores[num] = score
        
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        red_prediction = sorted([num for num, score in sorted_candidates[:6]])
        
        while len(red_prediction) < 6:
            for num in range(1, 34):
                if num not in red_prediction:
                    red_prediction.append(num)
                    if len(red_prediction) == 6:
                        break
        
        # 蓝球预测
        recent_blues = self.blue_balls[:20]
        blue_counter = Counter(recent_blues)
        
        blue_missing = {}
        for num in range(1, 17):
            blue_missing[num] = 0
            for blue in self.blue_balls:
                if blue == num:
                    break
                blue_missing[num] += 1
        
        blue_scores = {}
        for num in range(1, 17):
            score = blue_counter.get(num, 0) * weight_factor
            if blue_missing[num] > 8:
                score += 3 * (2 - weight_factor)
            elif blue_missing[num] > 4:
                score += 2 * (2 - weight_factor)
            blue_scores[num] = score
        
        blue_prediction = max(blue_scores.items(), key=lambda x: x[1])[0]
        
        return sorted(red_prediction), blue_prediction
    
    def save_prediction(self, markov_result, folk_result):
        """保存预测结果"""
        prediction_record = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "markov": {
                "red_balls": markov_result[0],
                "blue_ball": markov_result[1]
            },
            "folk": {
                "red_balls": folk_result[0],
                "blue_ball": folk_result[1]
            },
            "method_weights": self.method_weights.copy(),
            "verified": False
        }
        
        self.prediction_history["predictions"].append(prediction_record)
        self.save_history()
        
        return prediction_record
    
    def verify_prediction(self, actual_red, actual_blue):
        """验证预测结果并更新权重"""
        if not self.prediction_history["predictions"]:
            print("❌ 没有待验证的预测记录")
            return
        
        # 找到最近的未验证预测
        latest_prediction = None
        for pred in reversed(self.prediction_history["predictions"]):
            if not pred["verified"]:
                latest_prediction = pred
                break
        
        if not latest_prediction:
            print("❌ 没有找到未验证的预测")
            return
        
        # 计算命中情况
        markov_red_hits = len(set(latest_prediction["markov"]["red_balls"]) & set(actual_red))
        markov_blue_hit = 1 if latest_prediction["markov"]["blue_ball"] == actual_blue else 0
        
        folk_red_hits = len(set(latest_prediction["folk"]["red_balls"]) & set(actual_red))
        folk_blue_hit = 1 if latest_prediction["folk"]["blue_ball"] == actual_blue else 0
        
        # 更新验证状态
        latest_prediction["verified"] = True
        latest_prediction["actual_result"] = {
            "red_balls": actual_red,
            "blue_ball": actual_blue
        }
        latest_prediction["accuracy"] = {
            "markov": {"red_hits": markov_red_hits, "blue_hit": markov_blue_hit},
            "folk": {"red_hits": folk_red_hits, "blue_hit": folk_blue_hit}
        }
        
        # 更新权重
        self.update_weights(markov_red_hits + markov_blue_hit, folk_red_hits + folk_blue_hit)
        
        # 保存历史
        self.save_history()
        
        print(f"\n✅ 预测验证完成:")
        print(f"🔬 马尔可夫链: 红球命中{markov_red_hits}个, 蓝球{'命中' if markov_blue_hit else '未中'}")
        print(f"🎯 民间方法: 红球命中{folk_red_hits}个, 蓝球{'命中' if folk_blue_hit else '未中'}")
        print(f"📊 权重调整: 马尔可夫{self.method_weights['markov']:.2f}, 民间{self.method_weights['folk']:.2f}")
        
        return latest_prediction
    
    def update_weights(self, markov_score, folk_score):
        """根据准确率更新方法权重"""
        total_score = markov_score + folk_score
        if total_score > 0:
            # 基于表现调整权重，但保持平衡
            markov_performance = markov_score / max(total_score, 1)
            folk_performance = folk_score / max(total_score, 1)
            
            # 平滑调整，避免极端变化
            adjustment_factor = 0.1
            self.method_weights["markov"] = (
                self.method_weights["markov"] * (1 - adjustment_factor) + 
                markov_performance * adjustment_factor
            )
            self.method_weights["folk"] = (
                self.method_weights["folk"] * (1 - adjustment_factor) + 
                folk_performance * adjustment_factor
            )
            
            # 归一化权重
            total_weight = sum(self.method_weights.values())
            for key in self.method_weights:
                self.method_weights[key] /= total_weight
    
    def show_history(self):
        """显示预测历史"""
        if not self.prediction_history["predictions"]:
            print("📝 暂无预测历史")
            return
        
        print(f"\n📊 预测历史 (共{len(self.prediction_history['predictions'])}次):")
        print("-" * 80)
        
        for i, pred in enumerate(reversed(self.prediction_history["predictions"][-5:]), 1):
            print(f"\n第{i}次预测 ({pred['timestamp']}):")
            print(f"🔬 马尔可夫: {' '.join(f'{n:02d}' for n in pred['markov']['red_balls'])} + {pred['markov']['blue_ball']:02d}")
            print(f"🎯 民间方法: {' '.join(f'{n:02d}' for n in pred['folk']['red_balls'])} + {pred['folk']['blue_ball']:02d}")
            
            if pred["verified"]:
                acc = pred["accuracy"]
                print(f"✅ 实际开奖: {' '.join(f'{n:02d}' for n in pred['actual_result']['red_balls'])} + {pred['actual_result']['blue_ball']:02d}")
                print(f"📈 命中情况: 马尔可夫{acc['markov']['red_hits']+acc['markov']['blue_hit']}/7, 民间{acc['folk']['red_hits']+acc['folk']['blue_hit']}/7")
            else:
                print("⏳ 待验证")

def main():
    print("=" * 60)
    print("自适应双色球预测系统")
    print("具有学习记忆功能，可根据开奖结果调整策略")
    print("=" * 60)
    
    predictor = AdaptiveLotteryPredictor()
    
    while True:
        print("\n请选择操作:")
        print("1. 进行预测")
        print("2. 验证预测结果")
        print("3. 查看预测历史")
        print("4. 退出")
        
        choice = input("请输入选项 (1-4): ").strip()
        
        if choice == "1":
            # 进行预测
            file_path = input("请输入Excel文件路径（回车使用默认）: ").strip()
            if not file_path:
                file_path = "双色球开奖数据.xlsx"
            
            if predictor.load_data(file_path) and predictor.preprocess_data():
                print("\n🎲 正在预测...")
                markov_result = predictor.markov_prediction()
                folk_result = predictor.folk_prediction()
                
                # 保存预测
                prediction_record = predictor.save_prediction(markov_result, folk_result)
                
                print("\n" + "=" * 50)
                print("🎯 预测结果")
                print("=" * 50)
                print(f"🔬 马尔可夫链: {' '.join(f'{n:02d}' for n in markov_result[0])} + {markov_result[1]:02d}")
                print(f"🎯 民间方法: {' '.join(f'{n:02d}' for n in folk_result[0])} + {folk_result[1]:02d}")
                print(f"📊 当前权重: 马尔可夫{predictor.method_weights['markov']:.2f}, 民间{predictor.method_weights['folk']:.2f}")
                print("=" * 50)
        
        elif choice == "2":
            # 验证预测
            print("\n请输入实际开奖号码:")
            try:
                red_input = input("红球号码 (用空格分隔): ").strip().split()
                actual_red = [int(x) for x in red_input]
                actual_blue = int(input("蓝球号码: ").strip())
                
                if len(actual_red) == 6:
                    predictor.verify_prediction(actual_red, actual_blue)
                else:
                    print("❌ 请输入6个红球号码")
            except ValueError:
                print("❌ 输入格式错误，请输入数字")
        
        elif choice == "3":
            # 查看历史
            predictor.show_history()
        
        elif choice == "4":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()
