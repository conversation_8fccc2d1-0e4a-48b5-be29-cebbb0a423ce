#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球预测程序
结合马尔可夫链和民间方法进行双色球号码预测

使用方法：
1. 准备Excel格式的双色球历史数据
2. 运行程序：python lottery_predictor.py
3. 按提示上传数据文件
4. 查看预测结果

功能特点：
- 马尔可夫链数学预测
- 民间经验方法预测
- 数据可视化分析
- 多种预测策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import warnings
import os
from datetime import datetime
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class LotteryPredictor:
    """双色球预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.data = None
        self.red_balls = []  # 红球历史数据
        self.blue_balls = []  # 蓝球历史数据
        self.red_transition_matrix = None  # 红球转移矩阵
        self.blue_transition_matrix = None  # 蓝球转移矩阵
        
    def load_data(self, file_path):
        """加载Excel数据文件"""
        try:
            # 读取Excel文件
            self.data = pd.read_excel(file_path)
            print(f"✅ 成功加载数据，共 {len(self.data)} 条记录")

            # 显示数据结构
            print(f"数据列名: {list(self.data.columns)}")
            print(f"数据样例:\n{self.data.head()}")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def preprocess_data(self):
        """数据预处理"""
        try:
            # 提取红球和蓝球数据
            red_columns = [col for col in self.data.columns if '红球' in col]
            blue_columns = [col for col in self.data.columns if '蓝球' in col]

            print(f"红球列: {red_columns}")
            print(f"蓝球列: {blue_columns}")

            # 处理红球数据
            for _, row in self.data.iterrows():
                red_nums = []
                for col in red_columns:
                    if pd.notna(row[col]):
                        red_nums.append(int(row[col]))
                if len(red_nums) == 6:  # 确保有6个红球
                    self.red_balls.append(sorted(red_nums))

            # 处理蓝球数据
            for _, row in self.data.iterrows():
                for col in blue_columns:
                    if pd.notna(row[col]):
                        self.blue_balls.append(int(row[col]))
                        break  # 只取第一个蓝球

            print(f"✅ 数据预处理完成")
            print(f"红球数据: {len(self.red_balls)} 组")
            print(f"蓝球数据: {len(self.blue_balls)} 个")
            print(f"最近5期红球: {self.red_balls[:5]}")
            print(f"最近5期蓝球: {self.blue_balls[:5]}")

            return True

        except Exception as e:
            print(f"❌ 数据预处理失败: {e}")
            return False
        
    def markov_prediction(self):
        """马尔可夫链预测"""
        print("\n🔬 马尔可夫链预测分析...")

        # 红球马尔可夫链预测
        red_prediction = self._markov_red_prediction()

        # 蓝球马尔可夫链预测
        blue_prediction = self._markov_blue_prediction()

        return {
            'method': '马尔可夫链预测',
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'confidence': '基于状态转移概率'
        }

    def _markov_red_prediction(self):
        """红球马尔可夫链预测"""
        # 构建状态转移矩阵
        transition_count = defaultdict(lambda: defaultdict(int))

        # 统计相邻期次的号码转移
        for i in range(len(self.red_balls) - 1):
            current_set = set(self.red_balls[i])
            next_set = set(self.red_balls[i + 1])

            # 统计号码的出现和消失
            for num in range(1, 34):  # 红球范围1-33
                current_state = 1 if num in current_set else 0
                next_state = 1 if num in next_set else 0
                transition_count[num][(current_state, next_state)] += 1

        # 计算转移概率并预测
        predictions = []
        for num in range(1, 34):
            # 获取当前状态（最近一期是否包含该号码）
            current_state = 1 if num in set(self.red_balls[0]) else 0

            # 计算转移概率
            total_transitions = sum(transition_count[num].values())
            if total_transitions > 0:
                prob_appear = (transition_count[num][(current_state, 1)] / total_transitions)
                predictions.append((num, prob_appear))

        # 按概率排序，选择前6个
        predictions.sort(key=lambda x: x[1], reverse=True)
        selected_red = [num for num, prob in predictions[:6]]

        print(f"红球转移概率前10: {predictions[:10]}")
        return sorted(selected_red)

    def _markov_blue_prediction(self):
        """蓝球马尔可夫链预测"""
        # 构建蓝球转移矩阵
        transition_count = defaultdict(lambda: defaultdict(int))

        # 统计相邻期次蓝球转移
        for i in range(len(self.blue_balls) - 1):
            current_blue = self.blue_balls[i]
            next_blue = self.blue_balls[i + 1]
            transition_count[current_blue][next_blue] += 1

        # 基于最近一期蓝球预测下期
        last_blue = self.blue_balls[0]
        if last_blue in transition_count:
            # 找出转移概率最高的蓝球
            transitions = transition_count[last_blue]
            if transitions:
                predicted_blue = max(transitions.items(), key=lambda x: x[1])[0]
                print(f"蓝球 {last_blue} 的转移统计: {dict(transitions)}")
                return predicted_blue

        # 如果没有转移数据，选择出现频率最高的蓝球
        blue_counter = Counter(self.blue_balls)
        most_common_blue = blue_counter.most_common(1)[0][0]
        print(f"蓝球频率统计前5: {blue_counter.most_common(5)}")
        return most_common_blue
        
    def folk_prediction(self):
        """民间方法预测"""
        print("\n🎯 民间方法预测分析...")

        # 热号冷号分析
        hot_cold_result = self._hot_cold_analysis()

        # 和值分析
        sum_analysis_result = self._sum_analysis()

        # 遗漏值分析
        missing_analysis_result = self._missing_analysis()

        # 奇偶比分析
        odd_even_result = self._odd_even_analysis()

        # 综合民间方法预测
        red_prediction = self._comprehensive_folk_prediction(
            hot_cold_result, sum_analysis_result, missing_analysis_result, odd_even_result
        )

        # 蓝球民间预测
        blue_prediction = self._folk_blue_prediction()

        return {
            'method': '民间方法预测',
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'confidence': '基于热号冷号、和值、遗漏值综合分析',
            'details': {
                'hot_cold': hot_cold_result,
                'sum_analysis': sum_analysis_result,
                'missing_analysis': missing_analysis_result,
                'odd_even': odd_even_result
            }
        }

    def _hot_cold_analysis(self):
        """热号冷号分析"""
        # 统计最近30期的号码频率
        recent_periods = min(30, len(self.red_balls))
        recent_numbers = []

        for i in range(recent_periods):
            recent_numbers.extend(self.red_balls[i])

        number_freq = Counter(recent_numbers)

        # 热号（出现频率高）
        hot_numbers = [num for num, freq in number_freq.most_common(10)]

        # 冷号（出现频率低或未出现）
        all_numbers = set(range(1, 34))
        appeared_numbers = set(recent_numbers)
        cold_numbers = list(all_numbers - appeared_numbers)

        # 如果冷号不足，补充低频号码
        if len(cold_numbers) < 10:
            low_freq_numbers = [num for num, freq in number_freq.most_common()[-10:]]
            cold_numbers.extend(low_freq_numbers)
            cold_numbers = list(set(cold_numbers))[:10]

        print(f"热号前10: {hot_numbers}")
        print(f"冷号前10: {cold_numbers[:10]}")

        return {
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers[:10],
            'frequency': dict(number_freq.most_common(15))
        }

    def _sum_analysis(self):
        """和值分析"""
        # 计算历史和值
        sum_values = [sum(red_set) for red_set in self.red_balls]
        sum_counter = Counter(sum_values)

        # 分析和值范围
        avg_sum = np.mean(sum_values)
        std_sum = np.std(sum_values)

        # 推荐和值范围（均值±1个标准差）
        recommended_min = int(avg_sum - std_sum)
        recommended_max = int(avg_sum + std_sum)

        # 最常见的和值
        most_common_sums = sum_counter.most_common(5)

        print(f"历史和值统计: 平均={avg_sum:.1f}, 标准差={std_sum:.1f}")
        print(f"推荐和值范围: {recommended_min}-{recommended_max}")
        print(f"最常见和值: {most_common_sums}")

        return {
            'avg_sum': avg_sum,
            'recommended_range': (recommended_min, recommended_max),
            'most_common': most_common_sums,
            'recent_sums': sum_values[:10]
        }

    def _missing_analysis(self):
        """遗漏值分析"""
        # 计算每个号码的遗漏期数
        missing_count = {}

        for num in range(1, 34):
            missing_count[num] = 0
            # 从最新一期开始计算遗漏
            for red_set in self.red_balls:
                if num in red_set:
                    break
                missing_count[num] += 1

        # 按遗漏期数排序
        sorted_missing = sorted(missing_count.items(), key=lambda x: x[1], reverse=True)

        # 长期遗漏号码（可能要回补）
        long_missing = [num for num, count in sorted_missing[:10]]

        # 短期遗漏号码（可能继续遗漏）
        short_missing = [num for num, count in sorted_missing[-10:]]

        print(f"遗漏期数最长前10: {sorted_missing[:10]}")
        print(f"遗漏期数最短前10: {sorted_missing[-10:]}")

        return {
            'long_missing': long_missing,
            'short_missing': short_missing,
            'missing_stats': dict(sorted_missing)
        }

    def _odd_even_analysis(self):
        """奇偶比分析"""
        odd_even_ratios = []

        for red_set in self.red_balls:
            odd_count = sum(1 for num in red_set if num % 2 == 1)
            even_count = 6 - odd_count
            odd_even_ratios.append((odd_count, even_count))

        # 统计奇偶比分布
        ratio_counter = Counter(odd_even_ratios)
        most_common_ratio = ratio_counter.most_common(1)[0][0]

        print(f"奇偶比统计: {dict(ratio_counter)}")
        print(f"最常见奇偶比: 奇数{most_common_ratio[0]}个, 偶数{most_common_ratio[1]}个")

        return {
            'most_common_ratio': most_common_ratio,
            'ratio_distribution': dict(ratio_counter),
            'recent_ratios': odd_even_ratios[:10]
        }

    def _comprehensive_folk_prediction(self, hot_cold, sum_analysis, missing_analysis, odd_even):
        """综合民间方法预测"""
        # 候选号码池
        candidates = set()

        # 添加热号（权重高）
        candidates.update(hot_cold['hot_numbers'][:8])

        # 添加长期遗漏号码（可能回补）
        candidates.update(missing_analysis['long_missing'][:6])

        # 添加部分冷号（平衡策略）
        candidates.update(hot_cold['cold_numbers'][:4])

        # 转换为列表并评分
        candidate_list = list(candidates)
        scores = {}

        for num in candidate_list:
            score = 0

            # 热号加分
            if num in hot_cold['hot_numbers'][:5]:
                score += 3
            elif num in hot_cold['hot_numbers'][:10]:
                score += 2

            # 遗漏值加分
            missing_periods = missing_analysis['missing_stats'][num]
            if missing_periods > 10:  # 长期遗漏
                score += 2
            elif missing_periods > 5:  # 中期遗漏
                score += 1

            # 冷号适当加分（平衡策略）
            if num in hot_cold['cold_numbers'][:5]:
                score += 1

            scores[num] = score

        # 按评分排序选择前6个
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_candidates[:6]]

        # 如果不足6个，补充号码
        while len(selected_numbers) < 6:
            for num in range(1, 34):
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    if len(selected_numbers) == 6:
                        break

        # 验证奇偶比
        selected_set = set(selected_numbers)
        odd_count = sum(1 for num in selected_set if num % 2 == 1)
        target_odd, target_even = odd_even['most_common_ratio']

        print(f"综合评分前10: {sorted_candidates[:10]}")
        print(f"初选号码: {sorted(selected_numbers)}, 奇偶比: {odd_count}:{6-odd_count}")

        return sorted(selected_numbers)

    def _folk_blue_prediction(self):
        """蓝球民间预测"""
        # 蓝球热号分析
        recent_blues = self.blue_balls[:20]  # 最近20期
        blue_counter = Counter(recent_blues)

        # 蓝球遗漏分析
        blue_missing = {}
        for num in range(1, 17):  # 蓝球范围1-16
            blue_missing[num] = 0
            for blue in self.blue_balls:
                if blue == num:
                    break
                blue_missing[num] += 1

        # 综合评分
        blue_scores = {}
        for num in range(1, 17):
            score = 0

            # 频率加分
            freq = blue_counter.get(num, 0)
            score += freq

            # 遗漏期数加分
            missing = blue_missing[num]
            if missing > 8:  # 长期遗漏
                score += 3
            elif missing > 4:  # 中期遗漏
                score += 2

            blue_scores[num] = score

        # 选择评分最高的蓝球
        best_blue = max(blue_scores.items(), key=lambda x: x[1])[0]

        print(f"蓝球频率统计: {dict(blue_counter.most_common(8))}")
        print(f"蓝球遗漏统计前8: {sorted(blue_missing.items(), key=lambda x: x[1], reverse=True)[:8]}")
        print(f"蓝球综合评分前8: {sorted(blue_scores.items(), key=lambda x: x[1], reverse=True)[:8]}")

        return best_blue
        
    def visualize_analysis(self):
        """数据可视化分析"""
        print("\n📊 生成数据可视化图表...")

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('双色球数据分析可视化', fontsize=16, fontweight='bold')

        # 1. 红球号码频率分布
        all_red_numbers = []
        for red_set in self.red_balls:
            all_red_numbers.extend(red_set)

        red_freq = Counter(all_red_numbers)
        numbers = list(range(1, 34))
        frequencies = [red_freq.get(num, 0) for num in numbers]

        axes[0, 0].bar(numbers, frequencies, color='red', alpha=0.7)
        axes[0, 0].set_title('红球号码频率分布')
        axes[0, 0].set_xlabel('号码')
        axes[0, 0].set_ylabel('出现次数')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 蓝球号码频率分布
        blue_freq = Counter(self.blue_balls)
        blue_numbers = list(range(1, 17))
        blue_frequencies = [blue_freq.get(num, 0) for num in blue_numbers]

        axes[0, 1].bar(blue_numbers, blue_frequencies, color='blue', alpha=0.7)
        axes[0, 1].set_title('蓝球号码频率分布')
        axes[0, 1].set_xlabel('号码')
        axes[0, 1].set_ylabel('出现次数')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 和值分布
        sum_values = [sum(red_set) for red_set in self.red_balls]
        axes[0, 2].hist(sum_values, bins=20, color='green', alpha=0.7, edgecolor='black')
        axes[0, 2].set_title('红球和值分布')
        axes[0, 2].set_xlabel('和值')
        axes[0, 2].set_ylabel('频次')
        axes[0, 2].axvline(np.mean(sum_values), color='red', linestyle='--', label=f'平均值: {np.mean(sum_values):.1f}')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # 4. 奇偶比分布
        odd_even_ratios = []
        for red_set in self.red_balls:
            odd_count = sum(1 for num in red_set if num % 2 == 1)
            odd_even_ratios.append(f"{odd_count}:{6-odd_count}")

        ratio_counter = Counter(odd_even_ratios)
        ratios = list(ratio_counter.keys())
        counts = list(ratio_counter.values())

        axes[1, 0].pie(counts, labels=ratios, autopct='%1.1f%%', startangle=90)
        axes[1, 0].set_title('奇偶比分布')

        # 5. 号码遗漏期数
        missing_periods = []
        for num in range(1, 34):
            missing = 0
            for red_set in self.red_balls:
                if num in red_set:
                    break
                missing += 1
            missing_periods.append(missing)

        axes[1, 1].bar(range(1, 34), missing_periods, color='orange', alpha=0.7)
        axes[1, 1].set_title('红球号码遗漏期数')
        axes[1, 1].set_xlabel('号码')
        axes[1, 1].set_ylabel('遗漏期数')
        axes[1, 1].grid(True, alpha=0.3)

        # 6. 最近20期走势
        recent_data = self.red_balls[:20]
        trend_matrix = np.zeros((20, 33))

        for i, red_set in enumerate(recent_data):
            for num in red_set:
                trend_matrix[i, num-1] = 1

        im = axes[1, 2].imshow(trend_matrix, cmap='Reds', aspect='auto')
        axes[1, 2].set_title('最近20期红球走势图')
        axes[1, 2].set_xlabel('号码')
        axes[1, 2].set_ylabel('期次（从新到旧）')
        axes[1, 2].set_xticks(range(0, 33, 5))
        axes[1, 2].set_xticklabels(range(1, 34, 5))

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"双色球分析图表_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存: {chart_filename}")

        # 显示图表（可选）
        try:
            plt.show()
        except:
            print("无法显示图表，但已保存到文件")

        return chart_filename
        
    def generate_predictions(self):
        """生成预测结果"""
        print("\n🎲 开始生成预测...")

        # 马尔可夫链预测
        markov_result = self.markov_prediction()

        # 民间方法预测
        folk_result = self.folk_prediction()

        # 生成可视化图表
        chart_file = self.visualize_analysis()

        # 综合结果
        results = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'data_summary': {
                'total_periods': len(self.red_balls),
                'data_range': f"最新{len(self.red_balls)}期数据"
            },
            'markov_prediction': markov_result,
            'folk_prediction': folk_result,
            'chart_file': chart_file,
            'disclaimer': "预测仅供参考，彩票投注需谨慎，理性购彩"
        }

        return results
        
    def save_results(self, results, filename=None):
        """保存预测结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"双色球预测结果_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("双色球智能预测系统 - 预测结果报告\n")
                f.write("=" * 80 + "\n\n")

                f.write(f"预测时间: {results['timestamp']}\n")
                f.write(f"数据概况: {results['data_summary']['data_range']}\n")
                f.write(f"分析期数: {results['data_summary']['total_periods']}期\n\n")

                # 马尔可夫链预测结果
                markov = results['markov_prediction']
                f.write("🔬 马尔可夫链预测结果\n")
                f.write("-" * 40 + "\n")
                f.write(f"预测红球: {' '.join(f'{num:02d}' for num in markov['red_balls'])}\n")
                f.write(f"预测蓝球: {markov['blue_ball']:02d}\n")
                f.write(f"预测依据: {markov['confidence']}\n\n")

                # 民间方法预测结果
                folk = results['folk_prediction']
                f.write("🎯 民间方法预测结果\n")
                f.write("-" * 40 + "\n")
                f.write(f"预测红球: {' '.join(f'{num:02d}' for num in folk['red_balls'])}\n")
                f.write(f"预测蓝球: {folk['blue_ball']:02d}\n")
                f.write(f"预测依据: {folk['confidence']}\n\n")

                # 详细分析
                f.write("📊 详细分析数据\n")
                f.write("-" * 40 + "\n")

                details = folk['details']
                f.write(f"热号前5: {details['hot_cold']['hot_numbers'][:5]}\n")
                f.write(f"冷号前5: {details['hot_cold']['cold_numbers'][:5]}\n")
                f.write(f"推荐和值范围: {details['sum_analysis']['recommended_range']}\n")
                f.write(f"长期遗漏号码: {details['missing_analysis']['long_missing'][:5]}\n")
                f.write(f"推荐奇偶比: {details['odd_even']['most_common_ratio']}\n\n")

                # 免责声明
                f.write("⚠️  重要提醒\n")
                f.write("-" * 40 + "\n")
                f.write(f"{results['disclaimer']}\n")
                f.write("彩票具有随机性，任何预测方法都无法保证中奖\n")
                f.write("请根据个人经济能力理性购彩，切勿沉迷\n\n")

                f.write(f"可视化图表: {results['chart_file']}\n")
                f.write("=" * 80 + "\n")

            print(f"✅ 预测结果已保存: {filename}")

            # 在控制台显示主要结果
            self._display_results(results)

            return filename

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return None

    def _display_results(self, results):
        """在控制台显示预测结果"""
        print("\n" + "=" * 60)
        print("🎯 预测结果汇总")
        print("=" * 60)

        # 马尔可夫链预测
        markov = results['markov_prediction']
        print(f"\n🔬 马尔可夫链预测:")
        print(f"   红球: {' '.join(f'{num:02d}' for num in markov['red_balls'])}")
        print(f"   蓝球: {markov['blue_ball']:02d}")

        # 民间方法预测
        folk = results['folk_prediction']
        print(f"\n🎯 民间方法预测:")
        print(f"   红球: {' '.join(f'{num:02d}' for num in folk['red_balls'])}")
        print(f"   蓝球: {folk['blue_ball']:02d}")

        print(f"\n📊 数据基础: {results['data_summary']['data_range']}")
        print(f"📈 图表文件: {results['chart_file']}")

        print("\n" + "=" * 60)
        print("⚠️  预测仅供参考，理性购彩，量力而行")
        print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("双色球智能预测系统")
    print("结合马尔可夫链与民间方法")
    print("=" * 60)
    
    predictor = LotteryPredictor()
    
    # 获取数据文件路径
    file_path = input("请输入Excel数据文件路径（直接回车使用默认文件）: ").strip()
    if not file_path:
        file_path = "双色球开奖数据_20250804_164837.xlsx"
    
    try:
        # 加载和处理数据
        print(f"\n正在加载数据文件: {file_path}")
        predictor.load_data(file_path)
        predictor.preprocess_data()
        
        # 生成预测
        print("\n正在进行预测分析...")
        results = predictor.generate_predictions()
        
        # 显示结果
        print("\n" + "=" * 60)
        print("预测结果")
        print("=" * 60)
        
        # 保存结果
        predictor.save_results(results)
        
        print("\n预测完成！结果已保存。")
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        print("请检查数据文件格式是否正确")

if __name__ == "__main__":
    main()
