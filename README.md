# 中国福利彩票双色球开奖数据爬虫

## 功能介绍

这是一个用于获取中国福利彩票双色球历史开奖数据的Python爬虫程序。程序会自动从官方网站获取所有双色球开奖记录，并保存为Excel文件格式。

## 主要特点

- ✅ **完整数据获取**：自动获取所有历史双色球开奖数据
- ✅ **智能重试机制**：遇到网络问题自动重试，提高成功率
- ✅ **进度显示**：实时显示爬取进度
- ✅ **详细日志**：生成详细的运行日志，便于问题排查
- ✅ **Excel格式**：数据保存为Excel文件，便于查看和分析
- ✅ **反爬虫处理**：模拟真实浏览器请求，绕过基本的反爬虫机制

## 数据字段

获取的数据包含以下字段：
- 期号
- 开奖日期
- 星期
- 红球1-6（6个红球号码）
- 蓝球
- 销售额
- 奖池金额

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保已安装Python 3.6+
2. 安装依赖包
3. 运行程序：

```bash
python lottery_crawler.py
```

## 输出文件

- **Excel文件**：`双色球开奖数据_YYYYMMDD_HHMMSS.xlsx`
- **日志文件**：`lottery_crawler.log`

## 注意事项

1. **合理使用**：请勿频繁运行程序，避免对服务器造成压力
2. **网络环境**：建议在稳定的网络环境下运行
3. **运行时间**：完整数据获取可能需要较长时间，请耐心等待
4. **数据来源**：数据来源于中国福利彩票官方网站

## 配置说明

程序中的主要配置项（在`Config`类中）：

- `PAGE_SIZE`：每页获取的数据量（默认30）
- `MAX_RETRIES`：最大重试次数（默认3）
- `REQUEST_TIMEOUT`：请求超时时间（默认15秒）
- `MIN_DELAY`/`MAX_DELAY`：请求间隔时间（默认2-5秒）

## 故障排除

如果遇到问题，请：

1. 检查网络连接
2. 查看日志文件了解详细错误信息
3. 确认依赖包已正确安装
4. 如遇到403错误，程序会自动重试

## 免责声明

本程序仅用于学习和研究目的，请遵守相关网站的使用条款和法律法规。
