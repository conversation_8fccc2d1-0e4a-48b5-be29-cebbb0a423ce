#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国福利彩票双色球开奖数据爬虫
获取双色球历史开奖数据并保存到Excel文件

使用说明：
1. 安装依赖：pip install -r requirements.txt
2. 运行程序：python lottery_crawler.py
3. 程序会自动获取所有双色球开奖数据并保存到Excel文件

功能特点：
- 自动获取所有历史开奖数据
- 支持断点续传和错误重试
- 生成详细的日志文件
- 数据保存为Excel格式，便于查看和分析
- 包含红球、蓝球、开奖日期、销售额等完整信息

注意事项：
- 请合理使用，避免频繁请求
- 如遇到403错误，程序会自动重试
- 数据来源：中国福利彩票官网
"""

import requests
import pandas as pd
import json
import time
import random
from datetime import datetime
from tqdm import tqdm
import logging

# 配置类
class Config:
    """爬虫配置"""
    # API相关配置
    BASE_URL = "https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"
    MAIN_PAGE_URL = "https://www.cwl.gov.cn/ygkj/wqkjgg/ssq/"

    # 请求配置
    PAGE_SIZE = 30  # 每页数据量
    MAX_RETRIES = 3  # 最大重试次数
    REQUEST_TIMEOUT = 15  # 请求超时时间（秒）

    # 延时配置（秒）
    MIN_DELAY = 2
    MAX_DELAY = 5
    RETRY_DELAY_MIN = 3
    RETRY_DELAY_MAX = 8

    # 文件配置
    LOG_FILENAME = 'lottery_crawler.log'
    EXCEL_FILENAME_PREFIX = '双色球开奖数据'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILENAME, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class LotteryCrawler:
    """双色球数据爬虫类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.base_url = Config.BASE_URL
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': Config.MAIN_PAGE_URL,
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        self.params = {
            'name': 'ssq',
            'issueCount': '',
            'issueStart': '',
            'issueEnd': '',
            'dayStart': '',
            'dayEnd': '',
            'pageSize': str(Config.PAGE_SIZE),
            'week': '',
            'systemType': 'PC'
        }
        self.max_retries = Config.MAX_RETRIES
        
    def setup_session(self):
        """设置会话和请求头"""
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 首先访问主页面获取cookies
        try:
            response = self.session.get(Config.MAIN_PAGE_URL, timeout=Config.REQUEST_TIMEOUT)
            logging.info(f"访问主页面状态码: {response.status_code}")

            # 添加随机延时
            time.sleep(random.uniform(Config.MIN_DELAY, Config.MAX_DELAY))
            return True

        except Exception as e:
            logging.error(f"设置会话失败: {e}")
            return False
        
    def get_lottery_data(self, page_no=1):
        """获取指定页码的彩票数据，包含重试机制"""
        for attempt in range(self.max_retries):
            try:
                if not self.session:
                    if not self.setup_session():
                        continue

                # 设置分页参数
                params = self.params.copy()
                params['pageNo'] = str(page_no)

                # 发送请求
                response = self.session.get(
                    self.base_url,
                    params=params,
                    timeout=Config.REQUEST_TIMEOUT
                )

                logging.info(f"请求页码 {page_no}, 尝试 {attempt + 1}/{self.max_retries}, 状态码: {response.status_code}")

                if response.status_code == 200:
                    # 解析JSON响应
                    data = response.json()
                    return data
                elif response.status_code == 403:
                    logging.warning(f"访问被拒绝(403)，等待后重试...")
                    time.sleep(random.uniform(5, 10))
                    # 重新设置会话
                    self.session = None
                    continue
                else:
                    logging.error(f"请求失败，状态码: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logging.error(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
            except Exception as e:
                logging.error(f"未知错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")

            # 重试前等待
            if attempt < self.max_retries - 1:
                wait_time = random.uniform(Config.RETRY_DELAY_MIN, Config.RETRY_DELAY_MAX) * (attempt + 1)
                logging.info(f"等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

        logging.error(f"页码 {page_no} 获取失败，已达到最大重试次数")
        return None
        
    def parse_lottery_data(self, data):
        """解析彩票数据"""
        if not data or 'result' not in data:
            return []

        results = []

        try:
            # 获取数据列表
            lottery_list = data.get('result', [])

            for item in lottery_list:
                # 提取基本信息
                lottery_info = {
                    '期号': item.get('code', ''),
                    '开奖日期': item.get('date', ''),
                    '星期': item.get('week', ''),
                }

                # 解析开奖号码
                red_ball = item.get('red', '')
                blue_ball = item.get('blue', '')

                # 处理红球号码（通常是逗号分隔的6个数字）
                if red_ball:
                    red_numbers = red_ball.split(',')
                    for i, num in enumerate(red_numbers[:6], 1):
                        lottery_info[f'红球{i}'] = num.strip()

                # 处理蓝球号码
                if blue_ball:
                    lottery_info['蓝球'] = blue_ball.strip()

                # 添加其他可能的信息
                lottery_info['销售额'] = item.get('sales', '')
                lottery_info['奖池金额'] = item.get('poolmoney', '')

                results.append(lottery_info)

            logging.info(f"解析了 {len(results)} 条开奖记录")
            return results

        except Exception as e:
            logging.error(f"数据解析失败: {e}")
            return []
        
    def save_to_excel(self, data, filename='双色球开奖数据.xlsx'):
        """保存数据到Excel文件"""
        if not data:
            logging.warning("没有数据可保存")
            return False

        try:
            # 创建DataFrame
            df = pd.DataFrame(data)

            # 按期号排序（最新的在前）
            if '期号' in df.columns:
                df = df.sort_values('期号', ascending=False)

            # 重置索引
            df.reset_index(drop=True, inplace=True)

            # 保存到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='双色球开奖数据', index=False)

                # 获取工作表对象进行格式化
                worksheet = writer.sheets['双色球开奖数据']

                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 20)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logging.info(f"数据已保存到 {filename}, 共 {len(data)} 条记录")
            return True

        except Exception as e:
            logging.error(f"保存Excel文件失败: {e}")
            return False
        
    def crawl_all_data(self):
        """爬取所有双色球数据"""
        logging.info("开始爬取双色球开奖数据...")
        all_data = []
        page_no = 1

        # 首先获取第一页数据，确定总页数
        first_page_data = self.get_lottery_data(page_no)
        if not first_page_data:
            logging.error("无法获取第一页数据，爬取失败")
            return False

        # 解析第一页数据
        parsed_data = self.parse_lottery_data(first_page_data)
        all_data.extend(parsed_data)

        # 获取总记录数和总页数
        total_count = first_page_data.get('total', 0)
        page_size = int(self.params['pageSize'])
        total_pages = (total_count + page_size - 1) // page_size

        logging.info(f"总记录数: {total_count}, 总页数: {total_pages}")

        # 使用进度条爬取剩余页面
        if total_pages > 1:
            with tqdm(range(2, total_pages + 1), desc="爬取进度", unit="页") as pbar:
                for page_no in pbar:
                    pbar.set_description(f"正在爬取第 {page_no}/{total_pages} 页")

                    page_data = self.get_lottery_data(page_no)
                    if page_data:
                        parsed_data = self.parse_lottery_data(page_data)
                        all_data.extend(parsed_data)
                        pbar.set_postfix({"已获取": len(all_data)})
                    else:
                        logging.warning(f"第 {page_no} 页数据获取失败，跳过")

        logging.info(f"数据爬取完成，共获取 {len(all_data)} 条记录")

        # 保存数据到Excel
        if all_data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{Config.EXCEL_FILENAME_PREFIX}_{timestamp}.xlsx"
            success = self.save_to_excel(all_data, filename)
            if success:
                logging.info(f"数据已成功保存到 {filename}")
                return True
            else:
                logging.error("数据保存失败")
                return False
        else:
            logging.warning("没有获取到任何数据")
            return False

def main():
    """主函数"""
    print("=" * 50)
    print("中国福利彩票双色球开奖数据爬虫")
    print("=" * 50)

    try:
        # 创建爬虫实例
        crawler = LotteryCrawler()

        # 开始爬取数据
        success = crawler.crawl_all_data()

        if success:
            print("\n✅ 数据爬取完成！")
            print("📁 Excel文件已保存到当前目录")
        else:
            print("\n❌ 数据爬取失败，请查看日志文件了解详情")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        logging.error(f"主函数执行失败: {e}")

    print("=" * 50)

if __name__ == "__main__":
    main()
