# 双色球智能预测系统使用说明

## 系统概述

本系统结合马尔可夫链数学模型和民间经验方法，对双色球彩票进行智能预测分析。系统提供两种不同的预测方法，并生成详细的数据分析报告。

## 功能特点

### 🔬 马尔可夫链预测
- **理论基础**: 基于状态转移概率矩阵
- **分析方法**: 统计号码间的转移规律
- **适用场景**: 寻找数学统计规律

### 🎯 民间方法预测
- **热号冷号分析**: 统计近期高频和低频号码
- **和值分析**: 分析历史和值分布规律
- **遗漏值分析**: 计算号码遗漏期数
- **奇偶比分析**: 统计奇偶数比例分布

### 📊 数据可视化
- 号码频率分布图
- 和值分布直方图
- 奇偶比饼图
- 遗漏期数柱状图
- 走势图分析

## 安装依赖

```bash
pip install pandas numpy matplotlib seaborn openpyxl
```

## 使用方法

### 1. 准备数据文件
- 支持Excel格式(.xlsx)
- 数据应包含以下列：
  - 期号
  - 开奖日期
  - 红球1-6
  - 蓝球

### 2. 运行程序
```bash
python lottery_predictor.py
```

### 3. 输入数据文件路径
- 程序会提示输入Excel文件路径
- 可直接回车使用默认文件
- 支持相对路径和绝对路径

### 4. 查看结果
程序会自动生成：
- 控制台预测结果显示
- 详细预测报告文件(.txt)
- 数据分析图表(.png)

## 输出文件说明

### 预测报告文件
- **文件名**: `双色球预测结果_YYYYMMDD_HHMMSS.txt`
- **内容**: 
  - 马尔可夫链预测结果
  - 民间方法预测结果
  - 详细分析数据
  - 免责声明

### 可视化图表
- **文件名**: `双色球分析图表_YYYYMMDD_HHMMSS.png`
- **内容**: 6个分析图表的组合

## 预测方法详解

### 马尔可夫链方法
1. **状态定义**: 将号码出现/不出现作为状态
2. **转移矩阵**: 计算状态间转移概率
3. **预测逻辑**: 基于当前状态预测下期状态

### 民间方法综合评分
1. **热号权重**: 近期高频号码获得高分
2. **遗漏补偿**: 长期遗漏号码获得补偿分
3. **冷号平衡**: 适当考虑冷号的回补可能
4. **奇偶调节**: 参考历史奇偶比分布

## 数据要求

### Excel文件格式要求
```
期号 | 开奖日期 | 红球1 | 红球2 | 红球3 | 红球4 | 红球5 | 红球6 | 蓝球
-----|----------|-------|-------|-------|-------|-------|-------|------
2024001 | 2024-01-01 | 01 | 05 | 12 | 18 | 25 | 33 | 08
```

### 数据质量要求
- 数据完整性：无缺失值
- 数据准确性：号码范围正确
- 数据时序性：按时间顺序排列

## 注意事项

### ⚠️ 重要提醒
1. **预测仅供参考**: 彩票具有随机性，任何方法都无法保证中奖
2. **理性购彩**: 根据个人经济能力合理投注
3. **娱乐为主**: 将彩票视为娱乐方式，不要沉迷
4. **风险自担**: 投注风险由个人承担

### 技术限制
1. **样本依赖**: 预测准确性依赖历史数据质量
2. **随机性**: 彩票本质上是随机事件
3. **概率局限**: 无法改变基本中奖概率

## 常见问题

### Q: 预测准确率如何？
A: 根据学术研究，各种预测方法的准确率与随机选择相当，约为73%左右（3个号码以上匹配）。

### Q: 为什么要提供两种方法？
A: 马尔可夫链提供数学理论支撑，民间方法体现经验总结，两者结合可以从不同角度分析。

### Q: 数据文件格式有问题怎么办？
A: 请确保Excel文件包含必要的列名，数据格式正确，可参考程序生成的示例数据。

### Q: 图表无法显示怎么办？
A: 图表会自动保存为PNG文件，即使无法在屏幕显示也可以查看保存的文件。

## 技术支持

如遇到技术问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整安装
3. 数据文件格式是否正确
4. 文件路径是否存在

## 版本信息

- **版本**: 1.0
- **更新日期**: 2025-08-04
- **兼容性**: Python 3.6+

---

**免责声明**: 本系统仅供学习研究使用，预测结果不构成投注建议。彩票投注有风险，请理性购彩。
